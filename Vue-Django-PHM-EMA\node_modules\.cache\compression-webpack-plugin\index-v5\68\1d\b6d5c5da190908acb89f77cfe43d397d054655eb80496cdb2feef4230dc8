
4b6ea5ae34ae9ac43954643285cd4dd434ea2300	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f1192dc7f1f12ceccbb72b07c95e1dd2\"}","integrity":"sha512-N2E+sSSXegiy2oYFFevSalK+R/DuTM0JqvF9/cnBuzMDlhfbyd2dWRnSmjqGbbRO38QRRl+t7pgN7gYEgkJLJg==","time":1754405912825,"size":26733}