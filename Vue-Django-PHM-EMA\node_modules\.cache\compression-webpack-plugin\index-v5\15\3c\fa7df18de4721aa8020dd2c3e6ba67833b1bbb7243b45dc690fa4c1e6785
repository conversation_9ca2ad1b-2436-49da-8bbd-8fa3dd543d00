
99b52e5ddd7ad0cf2545bf1e7605e908083c71a6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"a39379ec8c89645947468866c83fda46\"}","integrity":"sha512-srARbvkSEjacRXcJdB1nh+o3dSUMJ1XRuFrmcZ7MxoD7xKpsXUPk6yeLXjInSOsk81davaCQUVC1M61Kwa+k8A==","time":1754405932276,"size":1855127}