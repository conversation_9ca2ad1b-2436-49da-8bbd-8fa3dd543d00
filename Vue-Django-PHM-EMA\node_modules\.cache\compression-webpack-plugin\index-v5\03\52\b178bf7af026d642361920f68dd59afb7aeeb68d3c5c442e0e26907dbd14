
2778dd6847a218c15506f7bd0c1ffbc35afdf893	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.e75d600ee2536c5ad829.hot-update.js\",\"contentHash\":\"4b91d7348bf0fe6d4fee7e23225062fe\"}","integrity":"sha512-nSdxSGG3Zn7ejetjGpeXSQ7zGgy/2T2xA0OyjN+EFgQraZfcJbABX1lUXOSTK9rpaoxlyV7OMvfMiUgfnYu3Og==","time":1754405932073,"size":20031}