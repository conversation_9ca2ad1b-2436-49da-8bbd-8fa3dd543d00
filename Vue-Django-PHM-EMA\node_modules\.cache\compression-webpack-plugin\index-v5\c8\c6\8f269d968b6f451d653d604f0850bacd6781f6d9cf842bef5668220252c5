
fc4bfae694b628ec90923c0e75d2481e0f531653	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"68b6606d1523e5c919d7fb26ca8a676f\"}","integrity":"sha512-IeccY/TEketDZn3aZ05dSGi2amyZ5qkH5kQ41suaHzXdfQI8VoEudM0g3IXRb42ktmMzGQ7a0sa+4MpXjfp2Ew==","time":1754405916041,"size":873469}