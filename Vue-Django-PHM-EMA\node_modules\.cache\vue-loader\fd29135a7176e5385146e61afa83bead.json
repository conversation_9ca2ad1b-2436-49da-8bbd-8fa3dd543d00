{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\Navbar.vue", "mtime": 1754405930003}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1634627658274}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n    <breadcrumb class=\"breadcrumb-container\" />\n    <div class=\"right-menu\">\n      <div class=\"time-display\">\n        <svg-icon icon-class=\"time\" />\n        {{ time }}\n      </div>\n      <div class=\"user-name\">\n        {{ name }}\n      </div>\n      <el-tooltip content=\"全屏显示\" placement=\"bottom\" effect=\"light\">\n        <screenfull\n          id=\"screenfull\"\n          class=\"right-menu-item hover-effect\"\n          style=\"color: #3f9af8;font-size: 18px;\"\n        />\n      </el-tooltip>\n      <el-dropdown class=\"avatar-container\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\" class=\"user-dropdown\">\n          <router-link to=\"/home/<USER>\">\n            <el-dropdown-item>\n              主控台\n            </el-dropdown-item>\n          </router-link>\n          <el-dropdown-item divided @click.native=\"userCenter\">\n            <span style=\"display:block;\">个人中心</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"changePassword\">\n            <span style=\"display:block;\">修改密码</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span style=\"display:block;\">注销</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    Screenfull\n  },\n  data() {\n    return {\n      time: '',\n      name: 'admin'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar'\n    ])\n  },\n  created() {\n    setInterval(this.nowTime, 1000)\n    this.name = this.$store.state.user.name\n  },\n  beforeDestroy() {\n    if (this.nowTime) {\n      clearInterval(this.nowTime) // 在Vue实例销毁前，清除时间定时器\n    }\n  },\n  methods: {\n    nowTime() {\n      this.time = this.$moment(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss')\n    },\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      console.log(this.$store.state.user.token)\n      // await this.$store.dispatch('user/logout')\n\n      if (this.$store.state.user.token) {\n        console.log('正常退出')\n        await this.$store.dispatch('user/logout')\n        this.$router.push(`/login`)\n      } else {\n        console.log('非正常退出reset')\n        await this.$store.dispatch('user/resetToken')\n        this.$router.push(`/login`)\n      }\n      // this.$store.state.tagsView.visitedViews = []\n      // this.$store.state.tagsView.cachedViews = []\n      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n\n      this.$router.push(`/login`)\n    },\n    userCenter() {\n      this.$router.push(`/userCenter/index`)\n    },\n    changePassword() {\n      this.$router.push('/userPassword/index')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.navbar {\n  height: 60px;\n  overflow: hidden;\n  position: relative;\n  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid $borderPrimary;\n  box-shadow: $shadowPrimary;\n\n  // 确保与TagsView的视觉连贯性\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, $borderPrimary, transparent);\n  }\n\n  .hamburger-container {\n    line-height: 60px; /* 与navbar高度一致，确保垂直居中 */\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n    -webkit-tap-highlight-color: transparent;\n    padding: 0 15px;\n    border-radius: 8px;\n    margin: 0; /* 移除margin，确保与breadcrumb对齐 */\n    display: flex; /* 使用flex布局确保内容居中 */\n    align-items: center; /* 垂直居中 */\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.1);\n      transform: scale(1.05);\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n    margin-left: 10px;\n    display: flex; /* 使用flex布局 */\n    align-items: center; /* 垂直居中，与hamburger按钮对齐 */\n    height: 100%; /* 占满导航栏高度 */\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 60px;\n    display: flex;\n    align-items: center;\n    padding-right: 20px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .time-display {\n      display: flex;\n      align-items: center;\n      color: $textSecondary;\n      font-weight: 500;\n      margin-right: 20px;\n      padding: 8px 16px;\n      background: rgba(0, 212, 255, 0.1);\n      border-radius: 20px;\n      border: 1px solid rgba(0, 212, 255, 0.2);\n      font-size: 14px;\n\n      .svg-icon {\n        margin-right: 8px;\n        color: $techBlue;\n      }\n    }\n\n    .user-name {\n      color: $techBlue;\n      font-weight: 600;\n      margin-right: 20px;\n      padding: 8px 16px;\n      background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n      border-radius: 20px;\n      border: 1px solid rgba(0, 212, 255, 0.3);\n      font-size: 14px;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 12px;\n      height: 40px;\n      line-height: 40px;\n      font-size: 18px;\n      color: $textSecondary;\n      vertical-align: text-bottom;\n      border-radius: 8px;\n      margin: 0 4px;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1);\n          color: $techBlue;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 0;\n\n      .avatar-wrapper {\n        margin-top: 0;\n        position: relative;\n        display: flex;\n        align-items: center;\n        padding: 8px 16px;\n        background: rgba(255, 255, 255, 0.05);\n        border-radius: 25px;\n        border: 1px solid $borderSecondary;\n        transition: all 0.3s ease;\n        cursor: pointer;\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1);\n          border-color: $borderHover;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);\n        }\n\n        .user-avatar {\n          cursor: pointer;\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          border: 2px solid $techBlue;\n          margin-right: 8px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: static;\n          font-size: 12px;\n          color: $textSecondary;\n          transition: all 0.3s ease;\n        }\n\n        &:hover .el-icon-caret-bottom {\n          color: $techBlue;\n          transform: rotate(180deg);\n        }\n      }\n    }\n  }\n}\n\n// 下拉菜单样式优化\n:deep(.user-dropdown) {\n  background: $bgCard !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary !important;\n  border-radius: 12px !important;\n  box-shadow: $shadowPrimary !important;\n  margin-top: 8px;\n\n  .el-dropdown-menu__item {\n    color: $textSecondary !important;\n    padding: 12px 20px !important;\n    transition: all 0.3s ease !important;\n    border-radius: 8px !important;\n    margin: 4px 8px !important;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.1) !important;\n      color: $techBlue !important;\n    }\n\n    &:not(:last-child) {\n      border-bottom: 1px solid $borderSecondary !important;\n    }\n  }\n}\n</style>\n"]}]}