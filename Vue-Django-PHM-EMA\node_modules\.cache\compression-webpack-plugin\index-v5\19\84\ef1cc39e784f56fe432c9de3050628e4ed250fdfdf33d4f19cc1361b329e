
bb625cfb8b64a49013650e8160edeca113a54c09	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"c6222e082f6bf7abbf64406d82554e6d\"}","integrity":"sha512-xMUt2iVS9yARlN2EvU+fO4TAYccMYq6H930yuoI06nWU4NMDbQf5qN0sA4XZzsUH+nJta0a8fvXRDidzzFB+YQ==","time":1754406267768,"size":1856695}