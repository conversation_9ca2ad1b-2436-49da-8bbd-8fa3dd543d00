
a8cec59b6a21dbb40515c603875818b9047b9e58	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"f1192dc7f1f12ceccbb72b07c95e1dd2\"}","integrity":"sha512-3EtASuz1SpvJHn5nIC11LG1MQMK9FW18KMYZLGrQIsBrRNGseMuiVfws68DjCwdmEYuzQQHiGocYA0qceIhKvg==","time":1754405913355,"size":23560}