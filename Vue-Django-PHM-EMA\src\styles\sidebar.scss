#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background: linear-gradient(180deg, $bgSecondary 0%, $bgPrimary 100%);
    backdrop-filter: blur(10px);
    border-right: 1px solid $borderPrimary;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
      transition: all 0.3s ease;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
      transition: all 0.3s ease;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      background: transparent !important;
    }

    // 现代化菜单项样式
    .submenu-title-noDropdown,
    .el-submenu__title {
      position: relative;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-radius: 8px;
      margin: 4px 8px;

      &:hover {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05)) !important;
        border: 1px solid rgba(0, 212, 255, 0.2);
        transform: translateX(4px);

        .svg-icon, .sub-el-icon {
          color: $techBlue;
          transform: scale(1.1);
        }
      }

      // 活跃状态发光效果
      &.is-active {
        background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(0, 212, 255, 0.08)) !important;
        border: 1px solid rgba(0, 212, 255, 0.3);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 60%;
          background: linear-gradient(180deg, $techBlue, $techBlueLight);
          border-radius: 2px;
        }
      }
    }

    .is-active>.el-submenu__title {
      color: $subMenuActiveText !important;
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;
      border-radius: 6px;
      margin: 2px 12px;
      transition: all 0.3s ease;

      &:hover {
        background: $subMenuHover !important;
        transform: translateX(4px);
        border: 1px solid rgba(0, 212, 255, 0.1);
      }

      &.is-active {
        background: rgba(0, 212, 255, 0.1) !important;
        color: $techBlue !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
      display: flex !important; /* 使用flex布局 */
      justify-content: center !important; /* 水平居中 */
      align-items: center !important; /* 垂直居中 */

      .el-tooltip {
        padding: 0 !important;
        display: flex !important; /* 使用flex布局 */
        justify-content: center !important; /* 水平居中 */
        align-items: center !important; /* 垂直居中 */
        width: 100% !important; /* 占满容器宽度 */

        .svg-icon {
          margin-left: 0 !important; /* 移除左边距，让flex居中生效 */
          margin-right: 0 !important; /* 移除右边距 */
        }

        .sub-el-icon {
          margin-left: 0 !important; /* 移除左边距，让flex居中生效 */
          margin-right: 0 !important; /* 移除右边距 */
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;
        display: flex !important; /* 使用flex布局 */
        justify-content: center !important; /* 水平居中 */
        align-items: center !important; /* 垂直居中 */

        .svg-icon {
          margin-left: 0 !important; /* 移除左边距，让flex居中生效 */
          margin-right: 0 !important; /* 移除右边距 */
        }

        .sub-el-icon {
          margin-left: 0 !important; /* 移除左边距，让flex居中生效 */
          margin-right: 0 !important; /* 移除右边距 */
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
