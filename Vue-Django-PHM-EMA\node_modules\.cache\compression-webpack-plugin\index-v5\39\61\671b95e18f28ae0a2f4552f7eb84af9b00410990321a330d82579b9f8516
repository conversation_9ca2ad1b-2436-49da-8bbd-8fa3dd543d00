
1531c9ca3c42e98af0520084df92ad0d4748321c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9b3a5c94a7d812c1469c14ad00f9d968\"}","integrity":"sha512-svY7JfIWvDBeinLVLOqhqrZillvj0qWfS/6GhVlj9Q+6xTfxyBm75XCF8adm4IRym8aUtIu9/xWNQ+ZBEx+LlA==","time":1754405932072,"size":26734}