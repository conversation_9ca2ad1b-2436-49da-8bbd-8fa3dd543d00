
00ce34ec868e8ab86f22fc35f51ba4ce6009bf2c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"fa619d494412e5a89e76b7f5ca49cb9f\"}","integrity":"sha512-+Dt3EYAobC1FW6vWh1PspD4BH1YoFi10FlxzSs5qaxS1gRWHGtW6Wxa7GYg/wCjd31ddOqOOUZwkVmc4FrGItw==","time":1754406096345,"size":23496}