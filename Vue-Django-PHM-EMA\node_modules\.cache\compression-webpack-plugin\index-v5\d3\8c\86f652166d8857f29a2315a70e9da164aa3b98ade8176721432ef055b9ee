
bb5793cfd68b30781f049350e848ed2f37825fcd	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"6da4f889bb7d9f490c9bc0772a90799a\"}","integrity":"sha512-TzrKoQXuU9s2C41dOrJR87r9YWNPozQOeoCinyWhlI7m1+db+vzFUEvbyNIUuisO7NNQLxrR2AJteLb0u58otQ==","time":1754406267587,"size":26725}