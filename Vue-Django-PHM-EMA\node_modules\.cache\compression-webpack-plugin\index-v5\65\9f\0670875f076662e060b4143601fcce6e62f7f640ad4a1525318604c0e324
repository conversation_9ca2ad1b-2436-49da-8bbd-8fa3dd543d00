
28d59ffdee9e4c09e535ae2ed2a1ee55b8c3c617	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"fa619d494412e5a89e76b7f5ca49cb9f\"}","integrity":"sha512-ImeFNS0IjBPPpwmvOYF8FxLNdqskHT5gbr+/dA/3vK/Sxv4eN4+15wgvmZi7hIfUQLJqU/UFdHdpioUvFmPQIA==","time":1754406095840,"size":26777}