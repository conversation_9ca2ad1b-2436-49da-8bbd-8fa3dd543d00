
cddf7d13d609863aa87f7b1debc29e5f5608bf3c	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"68b6606d1523e5c919d7fb26ca8a676f\"}","integrity":"sha512-/qME0ger0nINhGFiPenHf2HzZTAFYj8eCRuxQbAL8z4kaAoNkLX5bIcoEVHr4LP/jjRcqhY6bFpm+Kw1gQc9lA==","time":1754405913001,"size":1851051}