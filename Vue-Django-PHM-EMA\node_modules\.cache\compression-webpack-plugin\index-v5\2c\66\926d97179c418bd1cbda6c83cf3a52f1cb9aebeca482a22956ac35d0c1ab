
d5e503fc47a947c81265a3388b397f0057924269	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.ca09a06c98756d74bef1.hot-update.js\",\"contentHash\":\"850d44eb4ecba5e1614f7bfa25a8b874\"}","integrity":"sha512-5S4qWg09yi5d3ecGQ766zMwAM6Gj4XAV4LkXQjw5scpV5eS8wwqSI46m6M2QSdhPs4tXvrv9ps2ywH1el+mcww==","time":1754406268179,"size":46407}