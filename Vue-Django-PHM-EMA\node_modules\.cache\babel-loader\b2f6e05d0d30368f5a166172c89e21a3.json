{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\layout\\components\\Navbar.vue", "mtime": 1754405930003}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA,SAAA,UAAA,QAAA,MAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AACA,OAAA,SAAA,MAAA,wBAAA;AACA,OAAA,UAAA,MAAA,yBAAA;AAEA,eAAA;AACA,EAAA,UAAA,EAAA;AACA,IAAA,UAAA,EAAA,UADA;AAEA,IAAA,SAAA,EAAA,SAFA;AAGA,IAAA,UAAA,EAAA;AAHA,GADA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,EADA;AAEA,MAAA,IAAA,EAAA;AAFA,KAAA;AAIA,GAXA;AAYA,EAAA,QAAA,oBACA,UAAA,CAAA,CACA,SADA,EAEA,QAFA,CAAA,CADA,CAZA;AAkBA,EAAA,OAlBA,qBAkBA;AACA,IAAA,WAAA,CAAA,KAAA,OAAA,EAAA,IAAA,CAAA;AACA,SAAA,IAAA,GAAA,KAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA;AACA,GArBA;AAsBA,EAAA,aAtBA,2BAsBA;AACA,QAAA,KAAA,OAAA,EAAA;AACA,MAAA,aAAA,CAAA,KAAA,OAAA,CAAA,CADA,CACA;AACA;AACA,GA1BA;AA2BA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,qBACA;AACA,WAAA,IAAA,GAAA,KAAA,OAAA,CAAA,IAAA,IAAA,GAAA,OAAA,EAAA,EAAA,MAAA,CAAA,qBAAA,CAAA;AACA,KAHA;AAIA,IAAA,aAJA,2BAIA;AACA,WAAA,MAAA,CAAA,QAAA,CAAA,mBAAA;AACA,KANA;AAOA,IAAA,MAPA,oBAOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,EADA,CAEA;;AAFA,qBAIA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAJA;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,GAAA,CAAA,MAAA;AALA;AAAA,uBAMA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CANA;;AAAA;AAOA,gBAAA,KAAA,CAAA,OAAA,CAAA,IAAA;;AAPA;AAAA;;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AATA;AAAA,uBAUA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAVA;;AAAA;AAWA,gBAAA,KAAA,CAAA,OAAA,CAAA,IAAA;;AAXA;AAaA;AACA;AACA;AAEA,gBAAA,KAAA,CAAA,OAAA,CAAA,IAAA;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KAzBA;AA0BA,IAAA,UA1BA,wBA0BA;AACA,WAAA,OAAA,CAAA,IAAA;AACA,KA5BA;AA6BA,IAAA,cA7BA,4BA6BA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,qBAAA;AACA;AA/BA;AA3BA,CAAA", "sourcesContent": ["<template>\n  <div class=\"navbar\">\n    <hamburger :is-active=\"sidebar.opened\" class=\"hamburger-container\" @toggleClick=\"toggleSideBar\" />\n    <breadcrumb class=\"breadcrumb-container\" />\n    <div class=\"right-menu\">\n      <div class=\"time-display\">\n        <svg-icon icon-class=\"time\" />\n        {{ time }}\n      </div>\n      <div class=\"user-name\">\n        {{ name }}\n      </div>\n      <el-tooltip content=\"全屏显示\" placement=\"bottom\" effect=\"light\">\n        <screenfull\n          id=\"screenfull\"\n          class=\"right-menu-item hover-effect\"\n          style=\"color: #3f9af8;font-size: 18px;\"\n        />\n      </el-tooltip>\n      <el-dropdown class=\"avatar-container\" trigger=\"click\">\n        <div class=\"avatar-wrapper\">\n          <img :src=\"avatar\" class=\"user-avatar\">\n          <i class=\"el-icon-caret-bottom\" />\n        </div>\n        <el-dropdown-menu slot=\"dropdown\" class=\"user-dropdown\">\n          <router-link to=\"/home/<USER>\">\n            <el-dropdown-item>\n              主控台\n            </el-dropdown-item>\n          </router-link>\n          <el-dropdown-item divided @click.native=\"userCenter\">\n            <span style=\"display:block;\">个人中心</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"changePassword\">\n            <span style=\"display:block;\">修改密码</span>\n          </el-dropdown-item>\n          <el-dropdown-item divided @click.native=\"logout\">\n            <span style=\"display:block;\">注销</span>\n          </el-dropdown-item>\n        </el-dropdown-menu>\n      </el-dropdown>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport Breadcrumb from '@/components/Breadcrumb'\nimport Hamburger from '@/components/Hamburger'\nimport Screenfull from '@/components/Screenfull'\n\nexport default {\n  components: {\n    Breadcrumb,\n    Hamburger,\n    Screenfull\n  },\n  data() {\n    return {\n      time: '',\n      name: 'admin'\n    }\n  },\n  computed: {\n    ...mapGetters([\n      'sidebar',\n      'avatar'\n    ])\n  },\n  created() {\n    setInterval(this.nowTime, 1000)\n    this.name = this.$store.state.user.name\n  },\n  beforeDestroy() {\n    if (this.nowTime) {\n      clearInterval(this.nowTime) // 在Vue实例销毁前，清除时间定时器\n    }\n  },\n  methods: {\n    nowTime() {\n      this.time = this.$moment(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss')\n    },\n    toggleSideBar() {\n      this.$store.dispatch('app/toggleSideBar')\n    },\n    async logout() {\n      console.log(this.$store.state.user.token)\n      // await this.$store.dispatch('user/logout')\n\n      if (this.$store.state.user.token) {\n        console.log('正常退出')\n        await this.$store.dispatch('user/logout')\n        this.$router.push(`/login`)\n      } else {\n        console.log('非正常退出reset')\n        await this.$store.dispatch('user/resetToken')\n        this.$router.push(`/login`)\n      }\n      // this.$store.state.tagsView.visitedViews = []\n      // this.$store.state.tagsView.cachedViews = []\n      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)\n\n      this.$router.push(`/login`)\n    },\n    userCenter() {\n      this.$router.push(`/userCenter/index`)\n    },\n    changePassword() {\n      this.$router.push('/userPassword/index')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"~@/styles/variables.scss\";\n\n.navbar {\n  height: 60px;\n  overflow: hidden;\n  position: relative;\n  background: linear-gradient(135deg, $bgSecondary 0%, $bgPrimary 100%);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid $borderPrimary;\n  box-shadow: $shadowPrimary;\n\n  // 确保与TagsView的视觉连贯性\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent, $borderPrimary, transparent);\n  }\n\n  .hamburger-container {\n    line-height: 60px; /* 与navbar高度一致，确保垂直居中 */\n    height: 100%;\n    float: left;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n    -webkit-tap-highlight-color: transparent;\n    padding: 0 15px;\n    border-radius: 8px;\n    margin: 0; /* 移除margin，确保与breadcrumb对齐 */\n    display: flex; /* 使用flex布局确保内容居中 */\n    align-items: center; /* 垂直居中 */\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.1);\n      transform: scale(1.05);\n    }\n  }\n\n  .breadcrumb-container {\n    float: left;\n    margin-left: 10px;\n    display: flex; /* 使用flex布局 */\n    align-items: center; /* 垂直居中，与hamburger按钮对齐 */\n    height: 100%; /* 占满导航栏高度 */\n  }\n\n  .right-menu {\n    float: right;\n    height: 100%;\n    line-height: 60px;\n    display: flex;\n    align-items: center;\n    padding-right: 20px;\n\n    &:focus {\n      outline: none;\n    }\n\n    .time-display {\n      display: flex;\n      align-items: center;\n      color: $textSecondary;\n      font-weight: 500;\n      margin-right: 20px;\n      padding: 8px 16px;\n      background: rgba(0, 212, 255, 0.1);\n      border-radius: 20px;\n      border: 1px solid rgba(0, 212, 255, 0.2);\n      font-size: 14px;\n\n      .svg-icon {\n        margin-right: 8px;\n        color: $techBlue;\n      }\n    }\n\n    .user-name {\n      color: $techBlue;\n      font-weight: 600;\n      margin-right: 20px;\n      padding: 8px 16px;\n      background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05));\n      border-radius: 20px;\n      border: 1px solid rgba(0, 212, 255, 0.3);\n      font-size: 14px;\n    }\n\n    .right-menu-item {\n      display: inline-block;\n      padding: 0 12px;\n      height: 40px;\n      line-height: 40px;\n      font-size: 18px;\n      color: $textSecondary;\n      vertical-align: text-bottom;\n      border-radius: 8px;\n      margin: 0 4px;\n\n      &.hover-effect {\n        cursor: pointer;\n        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1);\n          color: $techBlue;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);\n        }\n      }\n    }\n\n    .avatar-container {\n      margin-right: 0;\n\n      .avatar-wrapper {\n        margin-top: 0;\n        position: relative;\n        display: flex;\n        align-items: center;\n        padding: 8px 16px;\n        background: rgba(255, 255, 255, 0.05);\n        border-radius: 25px;\n        border: 1px solid $borderSecondary;\n        transition: all 0.3s ease;\n        cursor: pointer;\n\n        &:hover {\n          background: rgba(0, 212, 255, 0.1);\n          border-color: $borderHover;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);\n        }\n\n        .user-avatar {\n          cursor: pointer;\n          width: 32px;\n          height: 32px;\n          border-radius: 50%;\n          border: 2px solid $techBlue;\n          margin-right: 8px;\n        }\n\n        .el-icon-caret-bottom {\n          cursor: pointer;\n          position: static;\n          font-size: 12px;\n          color: $textSecondary;\n          transition: all 0.3s ease;\n        }\n\n        &:hover .el-icon-caret-bottom {\n          color: $techBlue;\n          transform: rotate(180deg);\n        }\n      }\n    }\n  }\n}\n\n// 下拉菜单样式优化\n:deep(.user-dropdown) {\n  background: $bgCard !important;\n  backdrop-filter: blur(10px);\n  border: 1px solid $borderPrimary !important;\n  border-radius: 12px !important;\n  box-shadow: $shadowPrimary !important;\n  margin-top: 8px;\n\n  .el-dropdown-menu__item {\n    color: $textSecondary !important;\n    padding: 12px 20px !important;\n    transition: all 0.3s ease !important;\n    border-radius: 8px !important;\n    margin: 4px 8px !important;\n\n    &:hover {\n      background: rgba(0, 212, 255, 0.1) !important;\n      color: $techBlue !important;\n    }\n\n    &:not(:last-child) {\n      border-bottom: 1px solid $borderSecondary !important;\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/layout/components"}]}