
7d48983c416a42e0bd7e094ba37446bf01828b30	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"app.e75d600ee2536c5ad829.hot-update.js\",\"contentHash\":\"4b91d7348bf0fe6d4fee7e23225062fe\"}","integrity":"sha512-momcIZpEzHuWA75hQtEFo0IeRI9lT12aNlxPcMZMdNr28nlETxmuyC61U0W5EtE1rUx/OjWaZHCxa6xoobWMbQ==","time":1754405932581,"size":17948}