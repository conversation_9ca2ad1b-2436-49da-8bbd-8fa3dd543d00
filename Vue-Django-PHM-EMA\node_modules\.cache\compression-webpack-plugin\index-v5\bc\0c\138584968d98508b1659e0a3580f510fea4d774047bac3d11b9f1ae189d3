
cdc8ec57972be133f98d13dfc09bb37a006bc6c3	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"c6222e082f6bf7abbf64406d82554e6d\"}","integrity":"sha512-lP3Nq/RbJs24Bxrw6ah+B3v0MFeSitd9t2iN3ykUyaL5tpgRO9EDBC9TIFgnBLHJrTicn3dW1WU6pBBgM2s3kg==","time":1754406271143,"size":877604}