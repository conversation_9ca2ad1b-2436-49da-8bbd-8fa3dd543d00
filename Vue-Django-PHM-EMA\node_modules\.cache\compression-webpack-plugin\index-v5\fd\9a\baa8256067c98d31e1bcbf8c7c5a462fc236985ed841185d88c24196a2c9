
9ca80197cdc54cbc1f16d41a5e46115c04f1e711	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"6da4f889bb7d9f490c9bc0772a90799a\"}","integrity":"sha512-kdeWfuE0aiD3s0xgCx6X9I6hEN8rA1yAshLfjDhwzeQKSNxiUSPfmXUal8gL3WxL//omRekm9QKrjThim1SjyQ==","time":1754406268179,"size":23521}