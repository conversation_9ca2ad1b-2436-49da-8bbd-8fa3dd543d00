
cddf74f27ca555b750d91c8ae6c8822c526dcc48	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"app.ca09a06c98756d74bef1.hot-update.js\",\"contentHash\":\"850d44eb4ecba5e1614f7bfa25a8b874\"}","integrity":"sha512-cNSfLKao3mGy4d76/6TxtQua76YeOuoytb2HpxOkP9XS9Vbt0yU9AauCx+c9wAB9SNjgtJroH9wlDkHpsQIz9g==","time":1754406267587,"size":53810}