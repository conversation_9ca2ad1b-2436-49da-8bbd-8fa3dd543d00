
29da14c20ffc94fad694a223357b86b6448cf86d	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"9b3a5c94a7d812c1469c14ad00f9d968\"}","integrity":"sha512-VFOHSt/CTxTOkqX3JUX6mZRFH2J8ZCtZqSQr/bzBLQW7EPioi/b9+v+TI/ngR+hrPFjxrTqz/p81jT0RjC45wA==","time":1754405932581,"size":23579}