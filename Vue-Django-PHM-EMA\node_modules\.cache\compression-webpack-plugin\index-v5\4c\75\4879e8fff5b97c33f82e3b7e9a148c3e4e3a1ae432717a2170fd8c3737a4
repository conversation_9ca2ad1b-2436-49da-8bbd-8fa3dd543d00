
ac3287077c1cd39fe35b178465f50c0e1061d616	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"a39379ec8c89645947468866c83fda46\"}","integrity":"sha512-0Siu9sp/3UH2kqIFvI0BzERn0pT9wiu9VrnrbOxKro5+eSgZwUKqE4cmxyzdKImaGtInutwrFFmSdMnGsSg8Rw==","time":1754405935246,"size":876090}